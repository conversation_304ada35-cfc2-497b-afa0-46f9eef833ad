# Nginx configuration for XO Sports Hub Production
# This configuration handles large file uploads (1GB) with proper timeouts

server {
    listen 80;
    server_name api.xosportshub.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.xosportshub.com;
    
    # SSL Configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Large file upload configuration
    client_max_body_size 1024m;  # 1GB max upload size
    client_body_timeout 3600s;   # 1 hour timeout for client body
    client_header_timeout 60s;   # 60 seconds for headers
    
    # Proxy timeouts for large uploads
    proxy_connect_timeout 60s;
    proxy_send_timeout 3600s;    # 1 hour for sending data to backend
    proxy_read_timeout 3600s;    # 1 hour for reading response from backend
    proxy_request_buffering off; # Disable buffering for large uploads
    proxy_buffering off;         # Disable response buffering
    
    # CORS headers for all responses
    add_header 'Access-Control-Allow-Origin' 'https://xosportshub.com' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, PATCH, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
    
    # Handle preflight OPTIONS requests
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' 'https://xosportshub.com' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, PATCH, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain; charset=utf-8';
        add_header 'Content-Length' 0;
        return 204;
    }
    
    # API routes
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Special handling for upload endpoints
        location /api/content/upload {
            proxy_pass http://localhost:5000;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for large uploads
            proxy_connect_timeout 60s;
            proxy_send_timeout 3600s;
            proxy_read_timeout 3600s;
            proxy_request_buffering off;
            proxy_buffering off;
            
            # Large file upload settings
            client_max_body_size 1024m;
            client_body_timeout 3600s;
        }
        
        # Special handling for chunked upload endpoints
        location /api/content/upload/chunk {
            proxy_pass http://localhost:5000;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for chunk uploads
            proxy_connect_timeout 60s;
            proxy_send_timeout 1800s;  # 30 minutes per chunk
            proxy_read_timeout 1800s;  # 30 minutes per chunk
            proxy_request_buffering off;
            proxy_buffering off;
            
            # Chunk upload settings
            client_max_body_size 20m;  # 20MB max chunk size
            client_body_timeout 1800s; # 30 minutes per chunk
        }
    }
    
    # Static file serving
    location /uploads/ {
        alias /var/www/xosportshub/Backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Cross-Origin-Resource-Policy' 'cross-origin' always;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        access_log off;
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Logging
    access_log /var/log/nginx/xosportshub-api-access.log;
    error_log /var/log/nginx/xosportshub-api-error.log;
}

# Frontend server configuration
server {
    listen 80;
    server_name xosportshub.com www.xosportshub.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name xosportshub.com www.xosportshub.com;
    
    # SSL Configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Document root
    root /var/www/xosportshub/Frontend/dist;
    index index.html;
    
    # CORS headers for frontend
    add_header 'Cross-Origin-Opener-Policy' 'same-origin-allow-popups' always;
    add_header 'Cross-Origin-Embedder-Policy' 'unsafe-none' always;
    
    # Handle React Router
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Static assets with caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Logging
    access_log /var/log/nginx/xosportshub-frontend-access.log;
    error_log /var/log/nginx/xosportshub-frontend-error.log;
}
