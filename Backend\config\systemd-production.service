# Systemd service file for XO Sports Hub Production
# Place this file at: /etc/systemd/system/xosportshub.service
# 
# Commands to manage the service:
# sudo systemctl daemon-reload
# sudo systemctl enable xosportshub.service
# sudo systemctl start xosportshub.service
# sudo systemctl status xosportshub.service
# sudo systemctl restart xosportshub.service
# sudo systemctl stop xosportshub.service

[Unit]
Description=XO Sports Hub API Server
Documentation=https://github.com/your-org/xosportshub
After=network.target mongod.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/xosportshub/Backend
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10

# Environment variables
Environment=NODE_ENV=production
Environment=PORT=5000

# Resource limits for large file uploads
LimitNOFILE=65536
LimitNPROC=4096

# Memory and CPU limits
MemoryMax=2G
CPUQuota=200%

# Timeout settings for large uploads
TimeoutStartSec=60
TimeoutStopSec=30
TimeoutSec=infinity

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=xosportshub

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/xosportshub/Backend/uploads
ReadWritePaths=/var/www/xosportshub/Backend/temp

[Install]
WantedBy=multi-user.target
